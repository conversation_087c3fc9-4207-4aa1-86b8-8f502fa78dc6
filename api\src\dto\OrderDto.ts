import { User } from "../entities/User";
import { Customer } from "../entities/Customer";

interface IGarmentIntoOrder {
    id: number;
    quantity: number;
}
export interface ICreateOrderDto {
    customer: Customer;
    user: User;
    date: Date;
    time: string;
    deadline: string;
    status: string;
    garments: IGarmentIntoOrder[];
}

export interface IUpdateOrderDto {
    date?: Date;
    time?: string;
    deadline?: string;
    status?: string;
    customer_id?: number;
    staff_id?: number;
} 