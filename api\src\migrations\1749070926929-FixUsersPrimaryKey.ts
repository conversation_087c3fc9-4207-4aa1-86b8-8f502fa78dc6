import { MigrationInterface, QueryRunner } from "typeorm";

export class FixUsersPrimaryKey1749070926929 implements MigrationInterface {
    name = 'FixUsersPrimaryKey1749070926929'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Check if the primary key is already correct
        const result = await queryRunner.query(`
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
            WHERE TABLE_SCHEMA = 'db_malharias' 
            AND TABLE_NAME = 'users' 
            AND CONSTRAINT_NAME = 'PRIMARY'
        `);
        
        // If primary key is composite (id, person_id), fix it
        if (result.length > 1) {
            // Remove AUTO_INCREMENT temporarily
            await queryRunner.query(`ALTER TABLE \`users\` MODIFY \`id\` int NOT NULL`);
            
            // Drop and recreate primary key with only id
            await queryRunner.query(`ALTER TABLE \`users\` DROP PRIMARY KEY`);
            await queryRunner.query(`ALTER TABLE \`users\` ADD PRIMARY KEY (\`id\`)`);
            
            // Add AUTO_INCREMENT back
            await queryRunner.query(`ALTER TABLE \`users\` MODIFY \`id\` int NOT NULL AUTO_INCREMENT`);
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Revert to composite primary key
        await queryRunner.query(`ALTER TABLE \`users\` MODIFY \`id\` int NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`users\` DROP PRIMARY KEY`);
        await queryRunner.query(`ALTER TABLE \`users\` ADD PRIMARY KEY (\`id\`, \`person_id\`)`);
        await queryRunner.query(`ALTER TABLE \`users\` MODIFY \`id\` int NOT NULL AUTO_INCREMENT`);
    }
}
