import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>umn, PrimaryGeneratedColumn } from "typeorm";
import { Order } from "./Order";
import { Garment } from "./Garment";

@Entity("order_has_garments")
export class OrderHasGarments {
    @PrimaryGeneratedColumn()
    id!: number;

    @ManyToOne(() => Order, order => order.garments, { nullable: false, onDelete: "CASCADE" })
    @JoinColumn({ name: "order_id" })
    order!: Order;

    @ManyToOne(() => Garment, garment => garment.orderHasGarments, { nullable: false, onDelete: "CASCADE" })
    @JoinColumn({ name: "garment_id" })
    garment!: Garment;

    @Column()
    quantity!: number;
}