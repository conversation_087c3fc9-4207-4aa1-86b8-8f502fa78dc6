import type { Request, Response } from 'express';
import { garmentRepository } from "../repositories/garmentRepository";
import { ICreateGarmentDto, IPatchGarmentDto } from "../dto/GarmentDto";

class GarmentController {
    public async getAll(req: Request, res: Response) {
        try {
            const allGarments = await garmentRepository.find();
            res.status(200).json(allGarments);
        } catch (error) {
            console.error(error);
            res.status(500).json({ error: 'Internal Server Error' });
        }
    }

    public async getById(req: Request, res: Response) {
        const { id } = req.params;
        const garmentId = parseInt(id, 10);

        if (isNaN(garmentId)) {
            return res.status(400).json({ error: 'ID de modelo inválido.' });
        }

        try {
            const garment = await garmentRepository.findOneBy({ id: garmentId });

            if (!garment) {
                return res.status(404).json({ error: 'Modelo não encontrado!' });
            }
            
            res.status(200).json(garment);
        } catch (error) {
            console.error(error);
            res.status(500).json({ error: 'Internal Server Error' });
        }
    }

    public async create(req: Request, res: Response) {
        const { name, refcode, price, size, color, in_stock } = req.body;

        if (!isAllDataOk(name, refcode, price, size, color)) {
            return res.status(400).json({ error: 'Há campo(s) não preenchido(s)!' });
        }

        try {
            const garmentByRefCode = await garmentRepository.findOneBy({ refcode });
            if (garmentByRefCode) {
                return res.status(400).json({ error: 'Código de referência (Ref Code) já registrado em outro modelo anterior!' });
            }

            const garmentData: ICreateGarmentDto = {
                name,
                refcode,
                price,
                size,
                color,
                in_stock: in_stock || 0,
                is_active: true
            };

            const newGarment = garmentRepository.create(garmentData);
            const savedGarment = await garmentRepository.save(newGarment);

            return res.status(201).json({ 
                message: 'Modelo registrado com sucesso!', 
                Garment: savedGarment 
            });
        } catch (error) {
            console.error(error);
            res.status(500).json({ error: 'Internal Server Error' });
        }
    }

    public async patch(req: Request, res: Response) {
        const { id } = req.params;
        const garmentId = parseInt(id, 10);

        if (isNaN(garmentId)) {
            return res.status(400).json({ error: 'ID de modelo inválido ou faltando.' });
        }

        const { name, refcode, price, size, color, in_stock } = req.body;

        const garmentUpdatedData: IPatchGarmentDto = {
            ...(name && { name }),
            ...(refcode && { refcode }),
            ...(price !== undefined && { price }),
            ...(size && { size }),
            ...(color && { color }),
            ...(in_stock !== undefined && { in_stock })
        };

        if (!Object.keys(garmentUpdatedData).length) {
            return res.status(400).json({ error: 'Nenhum campo para atualizar!' });
        }

        try {
            const existingGarment = await garmentRepository.findOneBy({ id: garmentId });
            if (!existingGarment) {
                return res.status(404).json({ error: "Modelo não encontrado!" });
            }

            if (garmentUpdatedData.refcode) {
                const garmentByRefCode = await garmentRepository.findOneBy({ refcode: garmentUpdatedData.refcode });
                if (garmentByRefCode && garmentByRefCode.id !== garmentId) {
                    return res.status(400).json({ error: 'Código de referência (Ref Code) já registrado em outro modelo!' });
                }
            }

            await garmentRepository.update(garmentId, garmentUpdatedData);
            return res.status(200).json({ message: 'Modelo atualizado com sucesso!' });
        } catch (error) {
            console.error(error);
            res.status(500).json({ error: 'Internal Server Error' });
        }
    }

    public async delete(req: Request, res: Response) {
        const { id } = req.params;
        const garmentId = parseInt(id, 10);

        if (isNaN(garmentId)) {
            return res.status(400).json({ error: 'ID de modelo inválido ou faltando.' });
        }

        try {
            const garment = await garmentRepository.findOneBy({ id: garmentId });

            if (!garment) {
                return res.status(404).json({ error: 'Modelo não encontrado!' });
            }

            await garmentRepository.update(garmentId, { is_active: false });

            return res.status(200).json({ message: 'Modelo deletado com sucesso!' });
        } catch (error) {
            console.error(error);
            res.status(500).json({ error: 'Internal Server Error' });
        }
    }
}

function isAllDataOk(name: string, refcode: string, price: number, size: string, color: string): boolean {
    if (!name || !refcode || !price || !size || !color) {
        return false;
    }
    return true;
}

export default new GarmentController();