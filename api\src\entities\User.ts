import { <PERSON><PERSON><PERSON>, PrimaryGenerated<PERSON><PERSON>umn, Column, ManyToOne, JoinColumn, OneToMany, OneToOne } from "typeorm";
import { Person } from "./Person";
import { Order } from "./Order";
import { Staff } from "./Staff";

export enum UserType {
    STAFF = "staff",
    ADMIN = "admin"
}

@Entity("users")
export class User {
    @PrimaryGeneratedColumn()
    id!: number;

    @Column({ length: 100, nullable: true })
    username!: string;

    @Column({ length: 500, nullable: true })
    password!: string;

    @Column({
        type: "enum",
        enum: UserType,
        nullable: true
    })
    userType!: UserType;

    @Column()
    is_active!: boolean;

    @ManyToOne(() => Person, person => person.users, { nullable: false })
    @JoinColumn({ name: "person_id" })
    person!: Person;

    @OneToMany(() => Order, order => order.user, { nullable: false })
    orders!: Order[];

    @OneToOne(() => Staff, staff => staff.user, { nullable: true })
    staff?: Staff;
}