"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const customerRepository_1 = require("../repositories/customerRepository");
const personRepository_1 = require("../repositories/personRepository");
class CustomerController {
    getAll(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const allCustomers = yield customerRepository_1.customerRepository.find({
                    relations: {
                        person: true
                    }
                });
                res.status(200).json(allCustomers);
            }
            catch (error) {
                console.error(error);
                res.status(500).json({ error: 'Internal Server Error.' });
            }
        });
    }
    getById(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id } = req.params;
            const customerId = parseInt(id, 10);
            if (isNaN(customerId)) {
                return res.status(400).json({ message: 'ID do cliente inválido.' });
            }
            try {
                const customer = yield customerRepository_1.customerRepository.findOne({
                    where: { id: customerId },
                    relations: {
                        person: true
                    }
                });
                return customer ? res.status(200).json(customer) : res.status(404).json({ message: 'Cliente não encontrado.' });
            }
            catch (error) {
                console.error('Error fetching customer:', error);
                return res.status(500).json({ message: 'Error fetching customer' });
            }
        });
    }
    create(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            const { fullname, contact, cpf, cnpj, personType, address } = req.body;
            const personData = {
                fullname,
                contact,
                cpf,
                cnpj,
                personType
            };
            try {
                const newPerson = personRepository_1.personRepository.create(personData);
                const savedPerson = yield personRepository_1.personRepository.save(newPerson);
                const customerData = {
                    person: savedPerson,
                    address,
                    is_active: true
                };
                const newCustomer = customerRepository_1.customerRepository.create(customerData);
                yield customerRepository_1.customerRepository.save(newCustomer);
                res.status(201).json(newCustomer);
            }
            catch (error) {
                console.error(error);
                res.status(500).json({ error: 'Internal Server Error' });
            }
        });
    }
    patch(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id } = req.params;
            const customerId = parseInt(id, 10);
            if (isNaN(customerId)) {
                return res.status(400).json({ message: 'ID de cliente inválido!' });
            }
            const { fullname, contact, cpf, cnpj, address } = req.body;
            const personUpdatedData = Object.assign(Object.assign(Object.assign(Object.assign({}, (fullname && { fullname })), (contact && { contact })), (cpf !== undefined && { cpf })), (cnpj !== undefined && { cnpj }));
            const customerUpdatedData = Object.assign({}, (address && { address }));
            const wasPersonUpdated = Object.keys(personUpdatedData).length > 0;
            const wasCustomerUpdated = Object.keys(customerUpdatedData).length > 0;
            if (!wasPersonUpdated && !wasCustomerUpdated) {
                return res.status(400).json({ message: 'Nenhum campo para atualizar.' });
            }
            try {
                const customer = yield customerRepository_1.customerRepository.findOne({
                    where: { id: customerId },
                    relations: {
                        person: true
                    }
                });
                if (customer) {
                    const personId = customer.person.id;
                    if (wasPersonUpdated) {
                        yield personRepository_1.personRepository.update(personId, personUpdatedData);
                    }
                    if (wasCustomerUpdated) {
                        yield customerRepository_1.customerRepository.update(customerId, customerUpdatedData);
                    }
                    return res.status(200).json({ message: 'Cliente atualizado com sucesso!' });
                }
                else {
                    return res.status(404).json({ message: 'Cliente não encontrado!' });
                }
            }
            catch (error) {
                console.error('Erro ao atualizar cliente:', error);
                return res.status(500).json({ message: 'Erro ao atualizar cliente!' });
            }
        });
    }
    delete(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id } = req.params;
            const customerId = parseInt(id, 10);
            if (isNaN(customerId)) {
                return res.status(400).json({ message: 'ID de cliente inválido.' });
            }
            try {
                const customer = yield customerRepository_1.customerRepository.findOneBy({ id: customerId });
                if (!customer) {
                    return res.status(404).json({ message: 'Registro de cliente não encontrado.' });
                }
                yield customerRepository_1.customerRepository.update(customerId, { is_active: false });
                return res.status(200).json({ message: 'Cliente deletado com sucesso!' });
            }
            catch (error) {
                console.error('Erro ao deletar cliente:', error);
                return res.status(500).json({ message: 'Erro ao deletar cliente!' });
            }
        });
    }
}
exports.default = new CustomerController();
