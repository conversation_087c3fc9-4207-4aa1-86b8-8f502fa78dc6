import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, OneTo<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PrimaryColumn } from "typeorm";
import { User } from "./User";

@Entity("staff")
export class Staff {
    @PrimaryColumn()
    user_id!: number;

    @Column({ type: "decimal", precision: 7, scale: 2, nullable: true })
    comissionRate!: number;

    @OneToOne(() => User, user => user.staff, { nullable: false })
    @JoinColumn({ name: "user_id" })
    user!: User;
}