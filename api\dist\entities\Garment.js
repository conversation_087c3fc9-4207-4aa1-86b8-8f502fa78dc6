"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Garment = void 0;
const typeorm_1 = require("typeorm");
const RawMaterial_1 = require("./RawMaterial");
const OrderHasGarments_1 = require("./OrderHasGarments");
let Garment = class Garment {
};
exports.Garment = Garment;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], Garment.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50, unique: true }),
    __metadata("design:type", String)
], Garment.prototype, "refcode", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100 }),
    __metadata("design:type", String)
], Garment.prototype, "name", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50 }),
    __metadata("design:type", String)
], Garment.prototype, "color", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 50 }),
    __metadata("design:type", String)
], Garment.prototype, "size", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: "decimal", precision: 7, scale: 2 }),
    __metadata("design:type", Number)
], Garment.prototype, "price", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Boolean)
], Garment.prototype, "is_active", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], Garment.prototype, "in_stock", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => OrderHasGarments_1.OrderHasGarments, orderHasGarments => orderHasGarments.garment, { nullable: false }),
    __metadata("design:type", Array)
], Garment.prototype, "orderHasGarments", void 0);
__decorate([
    (0, typeorm_1.ManyToMany)(() => RawMaterial_1.RawMaterial, { nullable: false }),
    (0, typeorm_1.JoinTable)({
        name: "garments_uses_raw_material",
        joinColumn: {
            name: "garments_id",
            referencedColumnName: "id"
        },
        inverseJoinColumn: {
            name: "raw_material_id",
            referencedColumnName: "id"
        }
    }),
    __metadata("design:type", Array)
], Garment.prototype, "rawMaterials", void 0);
exports.Garment = Garment = __decorate([
    (0, typeorm_1.Entity)("garments")
], Garment);
