"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.orderRepository = void 0;
const data_source_1 = require("../data-source");
const Order_1 = require("../entities/Order");
const garmentRepository_1 = require("./garmentRepository");
const orderHasGamentsRepository_1 = require("./orderHasGamentsRepository");
exports.orderRepository = data_source_1.AppDataSource.getRepository(Order_1.Order).extend({
    saveOrderCompletely(order) {
        return __awaiter(this, void 0, void 0, function* () {
            const savedOrder = yield this.save(order);
            const garments = order.garments;
            garments.forEach((garment) => __awaiter(this, void 0, void 0, function* () {
                const garmentInstance = yield garmentRepository_1.garmentRepository.findOneBy({ id: garment.id });
                if (!garmentInstance) {
                    throw new Error(`Garment with id ${garment.id} not found`);
                }
                const newOrderHasGarmentInstance = {
                    order: savedOrder,
                    garment: garmentInstance,
                    quantity: garment.quantity
                };
                yield orderHasGamentsRepository_1.orderHasGarmentsRepository.save(newOrderHasGarmentInstance);
            }));
            return savedOrder;
        });
    }
});
