{"name": "api", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "ts-node-dev --inspect --transpile-only --ignore-watch node_modules src/server.ts", "build": "tsc", "migration:generate": "typeorm-ts-node-commonjs -d ./src/data-source.ts migration:generate ./src/migrations/default", "migration:run": "typeorm-ts-node-commonjs -d ./src/data-source.ts migration:run"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@biomejs/biome": "1.9.3", "@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/knex": "^0.16.1", "@types/mysql": "^2.15.26", "express": "^4.19.2", "ts-node-dev": "^2.0.0", "typescript": "^5.5.3"}, "dependencies": {"api": "file:", "bcrypt": "^5.1.1", "body-parser": "^1.20.2", "cors": "^2.8.5", "dayjs": "^1.11.13", "dotenv": "^16.4.5", "knex": "^3.1.0", "mysql2": "^3.10.3", "reflect-metadata": "^0.2.2", "typeorm": "^0.3.20"}}