"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
const garmentRepository_1 = require("../repositories/garmentRepository");
class GarmentController {
    getAll(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const allGarments = yield garmentRepository_1.garmentRepository.find();
                res.status(200).json(allGarments);
            }
            catch (error) {
                console.error(error);
                res.status(500).json({ error: 'Internal Server Error' });
            }
        });
    }
    getById(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id } = req.params;
            const garmentId = parseInt(id, 10);
            if (isNaN(garmentId)) {
                return res.status(400).json({ error: 'ID de modelo inválido.' });
            }
            try {
                const garment = yield garmentRepository_1.garmentRepository.findOneBy({ id: garmentId });
                if (!garment) {
                    return res.status(404).json({ error: 'Modelo não encontrado!' });
                }
                res.status(200).json(garment);
            }
            catch (error) {
                console.error(error);
                res.status(500).json({ error: 'Internal Server Error' });
            }
        });
    }
    create(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            const { name, refcode, price, size, color, in_stock } = req.body;
            if (!isAllDataOk(name, refcode, price, size, color)) {
                return res.status(400).json({ error: 'Há campo(s) não preenchido(s)!' });
            }
            try {
                const garmentByRefCode = yield garmentRepository_1.garmentRepository.findOneBy({ refcode });
                if (garmentByRefCode) {
                    return res.status(400).json({ error: 'Código de referência (Ref Code) já registrado em outro modelo anterior!' });
                }
                const garmentData = {
                    name,
                    refcode,
                    price,
                    size,
                    color,
                    in_stock: in_stock || 0,
                    is_active: true
                };
                const newGarment = garmentRepository_1.garmentRepository.create(garmentData);
                const savedGarment = yield garmentRepository_1.garmentRepository.save(newGarment);
                return res.status(201).json({
                    message: 'Modelo registrado com sucesso!',
                    Garment: savedGarment
                });
            }
            catch (error) {
                console.error(error);
                res.status(500).json({ error: 'Internal Server Error' });
            }
        });
    }
    patch(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id } = req.params;
            const garmentId = parseInt(id, 10);
            if (isNaN(garmentId)) {
                return res.status(400).json({ error: 'ID de modelo inválido ou faltando.' });
            }
            const { name, refcode, price, size, color, in_stock } = req.body;
            const garmentUpdatedData = Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, (name && { name })), (refcode && { refcode })), (price !== undefined && { price })), (size && { size })), (color && { color })), (in_stock !== undefined && { in_stock }));
            if (!Object.keys(garmentUpdatedData).length) {
                return res.status(400).json({ error: 'Nenhum campo para atualizar!' });
            }
            try {
                const existingGarment = yield garmentRepository_1.garmentRepository.findOneBy({ id: garmentId });
                if (!existingGarment) {
                    return res.status(404).json({ error: "Modelo não encontrado!" });
                }
                if (garmentUpdatedData.refcode) {
                    const garmentByRefCode = yield garmentRepository_1.garmentRepository.findOneBy({ refcode: garmentUpdatedData.refcode });
                    if (garmentByRefCode && garmentByRefCode.id !== garmentId) {
                        return res.status(400).json({ error: 'Código de referência (Ref Code) já registrado em outro modelo!' });
                    }
                }
                yield garmentRepository_1.garmentRepository.update(garmentId, garmentUpdatedData);
                return res.status(200).json({ message: 'Modelo atualizado com sucesso!' });
            }
            catch (error) {
                console.error(error);
                res.status(500).json({ error: 'Internal Server Error' });
            }
        });
    }
    delete(req, res) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id } = req.params;
            const garmentId = parseInt(id, 10);
            if (isNaN(garmentId)) {
                return res.status(400).json({ error: 'ID de modelo inválido ou faltando.' });
            }
            try {
                const garment = yield garmentRepository_1.garmentRepository.findOneBy({ id: garmentId });
                if (!garment) {
                    return res.status(404).json({ error: 'Modelo não encontrado!' });
                }
                yield garmentRepository_1.garmentRepository.update(garmentId, { is_active: false });
                return res.status(200).json({ message: 'Modelo deletado com sucesso!' });
            }
            catch (error) {
                console.error(error);
                res.status(500).json({ error: 'Internal Server Error' });
            }
        });
    }
}
function isAllDataOk(name, refcode, price, size, color) {
    if (!name || !refcode || !price || !size || !color) {
        return false;
    }
    return true;
}
exports.default = new GarmentController();
