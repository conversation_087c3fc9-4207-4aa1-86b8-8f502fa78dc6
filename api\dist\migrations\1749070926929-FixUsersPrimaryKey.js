"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FixUsersPrimaryKey1749070926929 = void 0;
class FixUsersPrimaryKey1749070926929 {
    constructor() {
        this.name = 'FixUsersPrimaryKey1749070926929';
    }
    up(queryRunner) {
        return __awaiter(this, void 0, void 0, function* () {
            // Check if the primary key is already correct
            const result = yield queryRunner.query(`
            SELECT COLUMN_NAME 
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
            WHERE TABLE_SCHEMA = 'db_malharias' 
            AND TABLE_NAME = 'users' 
            AND CONSTRAINT_NAME = 'PRIMARY'
        `);
            // If primary key is composite (id, person_id), fix it
            if (result.length > 1) {
                // Remove AUTO_INCREMENT temporarily
                yield queryRunner.query(`ALTER TABLE \`users\` MODIFY \`id\` int NOT NULL`);
                // Drop and recreate primary key with only id
                yield queryRunner.query(`ALTER TABLE \`users\` DROP PRIMARY KEY`);
                yield queryRunner.query(`ALTER TABLE \`users\` ADD PRIMARY KEY (\`id\`)`);
                // Add AUTO_INCREMENT back
                yield queryRunner.query(`ALTER TABLE \`users\` MODIFY \`id\` int NOT NULL AUTO_INCREMENT`);
            }
        });
    }
    down(queryRunner) {
        return __awaiter(this, void 0, void 0, function* () {
            // Revert to composite primary key
            yield queryRunner.query(`ALTER TABLE \`users\` MODIFY \`id\` int NOT NULL`);
            yield queryRunner.query(`ALTER TABLE \`users\` DROP PRIMARY KEY`);
            yield queryRunner.query(`ALTER TABLE \`users\` ADD PRIMARY KEY (\`id\`, \`person_id\`)`);
            yield queryRunner.query(`ALTER TABLE \`users\` MODIFY \`id\` int NOT NULL AUTO_INCREMENT`);
        });
    }
}
exports.FixUsersPrimaryKey1749070926929 = FixUsersPrimaryKey1749070926929;
