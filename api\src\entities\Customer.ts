import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, ManyToOne, <PERSON><PERSON>C<PERSON><PERSON>n, OneToMany } from "typeorm";
import { Person } from "./Person";
import { Order } from "./Order";

@Entity("customers")
export class Customer {
    @PrimaryGeneratedColumn()
    id!: number;

    @Column({ length: 200, nullable: true })
    address!: string;

    @Column({ nullable: false })
    is_active!: boolean;

    @ManyToOne(() => Person, person => person.customers, { nullable: false })
    @JoinColumn({ name: "person_id" })
    person!: Person;

    @OneToMany(() => Order, order => order.customer, { nullable: false })
    orders!: Order[];
}