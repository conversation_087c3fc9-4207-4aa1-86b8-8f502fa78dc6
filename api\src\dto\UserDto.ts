import { Person } from "../entities/Person";
import { UserType } from "../entities/User";

export interface ICreateUserDto {
    person: Person;
    username: string;
    password: string;
    userType: UserType;
    is_active: boolean;
    comissionRate?: number;
}

export interface IPatchUserDto {
    username?: string;
    password?: string;
    userType?: UserType;
    is_active?: boolean;
    person?: Person;
    comissionRate?: number;
} 
