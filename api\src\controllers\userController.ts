import type { Request, Response } from "express";
import { PersonType } from "../entities/Person";
import { UserType } from "../entities/User";
import { PersonService } from "../services/personService";
import { UserService } from "../services/userService";

class UserController {
	public async getAll(req: Request, res: Response) {
		try {
			const allUsers = await UserService.getAllUsers();
			res.status(200).json(allUsers);
		} catch (error) {
			console.error(error);
			res.status(500).json({ error: "Internal Server Error" });
		}
	}

	public async getById(req: Request, res: Response) {
		const { id } = req.params;
		const userId = parseInt(id, 10);

		if (isNaN(userId)) {
			return res.status(400).json({ error: "ID de usuário inválido!" });
		}

		try {
			const user = await UserService.getUserById(userId);
			if (!user) {
				return res.status(404).json({ error: "Usuário não encontrado!" });
			}

			res.status(200).json(user);
		} catch (error) {
			console.error(error);
			res.status(500).json({ error: "Internal Server Error" });
		}
	}

	public async create(req: Request, res: Response) {
		const {
			fullname,
			contact,
			cpf,
			cnpj,
			personType,
			username,
			password,
			userType,
			comissionRate,
		} = req.body;

		try {
			// Validation functions
			const isAllDataOk = (): boolean => {
				if (
					!fullname ||
					!contact ||
					!personType ||
					!username ||
					!password ||
					!userType
				) {
					return false;
				}
				if (!cpf && !cnpj) {
					return false;
				}
				return true;
			};

			const isPersonTypeOk = (): boolean => {
				return personType === "individual" || personType === "legal";
			};

			const isUserTypeOk = (): boolean => {
				return userType === "staff" || userType === "admin";
			};

			// Validations
			if (!isAllDataOk()) {
				return res
					.status(400)
					.json({ error: "Há campo(s) não preenchido(s)." });
			}
			if (!isPersonTypeOk()) {
				return res
					.status(400)
					.json({ error: "Tipo de pessoa (jurídica ou física) incorreto." });
			}
			if (!isUserTypeOk()) {
				return res.status(400).json({ error: "Tipo de usuário incorreto." });
			}

			// Check if username already exists
			const usernameAlreadyInUse = await UserService.checkUsername(username);
			if (usernameAlreadyInUse) {
				return res.status(400).json({ error: "Nome de usuário já em uso." });
			}

			// Check if CPF/CNPJ already exists
			const cpfExists = cpf ? await PersonService.checkCPForCNPJ("cpf", cpf) : false;
			const cnpjExists = cnpj ? await PersonService.checkCPForCNPJ("cnpj", cnpj) : false;

			if (cpfExists || cnpjExists) {
				return res.status(409).json({
					error: "CPF/CNPJ já cadastrado no sistema anteriormente",
				});
			}

			// Create person
			const person_id = await PersonService.registerPerson({
				fullname,
				contact,
				cpf,
				cnpj,
				personType: personType as PersonType,
			});

			// Create user
			await UserService.registerUser({
				username,
				password,
				userType,
				person_id,
				comissionRate,
			});

			return res.status(201).json({ message: "Usuário criado com sucesso!" });
		} catch (error) {
			console.error(error);
			res.status(500).json({ error: "Erro interno do servidor" });
		}
	}

	public async patch(req: Request, res: Response) {
		const { id } = req.params;
		const userId = Number.parseInt(id, 10);

		if (isNaN(userId)) {
			return res.status(400).json({ message: "ID de usuário inválido!" });
		}

		const {
			fullname,
			contact,
			cpf,
			cnpj,
			personType,
			username,
			password,
			userType,
			comissionRate,
		} = req.body;

		try {
			// Get current user
			const currentUser = await UserService.getUserById(userId);
			if (!currentUser) {
				return res.status(404).json({ message: "Usuário não encontrado!" });
			}

			// Prepare update data
			const updateData: any = {};

			if (username !== undefined) updateData.username = username;
			if (password !== undefined) updateData.password = password;
			if (userType !== undefined) updateData.userType = userType as UserType;
			if (comissionRate !== undefined) updateData.comissionRate = comissionRate;

			// Update person data if provided
			if (fullname !== undefined || contact !== undefined || cpf !== undefined ||
				cnpj !== undefined || personType !== undefined) {

				const personUpdateData: any = {};
				if (fullname !== undefined) personUpdateData.fullname = fullname;
				if (contact !== undefined) personUpdateData.contact = contact;
				if (cpf !== undefined) personUpdateData.cpf = cpf;
				if (cnpj !== undefined) personUpdateData.cnpj = cnpj;
				if (personType !== undefined) personUpdateData.personType = personType as PersonType;

				const updatedPerson = await PersonService.updatePerson(currentUser.person.id, personUpdateData);
				if (updatedPerson) {
					updateData.person = updatedPerson;
				}
			}

			// Check if there's anything to update
			if (Object.keys(updateData).length === 0) {
				return res.status(400).json({ message: "Nenhum campo para atualizar." });
			}

			// Update user
			const updatedUser = await UserService.updateUser(userId, updateData);
			if (updatedUser) {
				return res.status(200).json({ message: "Usuário atualizado com sucesso!" });
			} else {
				return res.status(404).json({ message: "Usuário não atualizado." });
			}
		} catch (error) {
			console.error("Erro ao atualizar usuário:", error);
			return res.status(500).json({ message: "Erro ao atualizar usuário!" });
		}
	}

	public async delete(req: Request, res: Response) {
		const { id } = req.params;
		const userId = parseInt(id, 10);

		if (isNaN(userId)) {
			return res.status(400).json({ message: "ID de usuário inválido!" });
		}

		try {
			const result = await UserService.deleteUser(userId);

			if (result) {
				return res
					.status(200)
					.json({ message: "Usuário deletado com sucesso!." });
			} else {
				return res.status(404).json({ message: "Usuário não encontrado." });
			}
		} catch (error) {
			console.error("Erro ao deletar usuário:", error);
			return res.status(500).json({ message: "Internal server error." });
		}
	}

	public async login(req: Request, res: Response) {
		const { username, password } = req.body;

		if (!username || !password) {
			return res.status(400).json({ error: "Username e password são obrigatórios!" });
		}

		try {
			const user = await UserService.login(username, password);

			if (!user) {
				return res
					.status(404)
					.json({ error: "Nome de usuário ou senha incorreto(s)!" });
			}

			return res.status(200).json(user);
		} catch (error) {
			console.error("Erro no login:", error);
			return res.status(500).json({ error: "Erro interno do servidor" });
		}
	}
}

export default new UserController();
