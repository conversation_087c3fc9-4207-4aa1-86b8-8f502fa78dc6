export interface ICreateGarmentDto {
    refcode: string;
    name: string;
    color: string;
    size: string;
    price: number;
    is_active: boolean;
    in_stock: number;
}

export interface IUpdateGarmentDto {
    refcode?: string;
    name?: string;
    color?: string;
    size?: string;
    price?: number;
    is_active?: boolean;
    in_stock?: number;
}

export interface IPatchGarmentDto {
    name?: string;
    refcode?: string;
    price?: number;
    size?: string;
    color?: string;
    in_stock?: number;
    is_active?: boolean;
}