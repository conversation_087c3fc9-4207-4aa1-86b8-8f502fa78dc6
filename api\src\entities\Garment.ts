import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, OneToMany, ManyToMany, JoinT<PERSON> } from "typeorm";
import { RawMaterial } from "./RawMaterial";
import { OrderHasGarments } from "./OrderHasGarments";

@Entity("garments")
export class Garment {
    @PrimaryGeneratedColumn()
    id!: number;

    @Column({ length: 50, unique: true })
    refcode!: string;

    @Column({ length: 100 })
    name!: string;

    @Column({ length: 50 })
    color!: string;

    @Column({ length: 50 })
    size!: string;

    @Column({ type: "decimal", precision: 7, scale: 2 })
    price!: number;

    @Column()
    is_active!: boolean;

    @Column()
    in_stock!: number;

    @OneToMany(() => OrderHasGarments, orderHasGarments => orderHasGarments.garment, { nullable: false })
    orderHasGarments!: OrderHasGarments[];

    @ManyToMany(() => RawMaterial, { nullable: false })
    @JoinTable({
        name: "garments_uses_raw_material",
        joinColumn: {
            name: "garments_id",
            referencedColumnName: "id"
        },
        inverseJoinColumn: {
            name: "raw_material_id",
            referencedColumnName: "id"
        }
    })
    rawMaterials!: RawMaterial[];
}