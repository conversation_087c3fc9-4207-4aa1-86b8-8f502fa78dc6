"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderHasGarments = void 0;
const typeorm_1 = require("typeorm");
const Order_1 = require("./Order");
const Garment_1 = require("./Garment");
let OrderHasGarments = class OrderHasGarments {
};
exports.OrderHasGarments = OrderHasGarments;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], OrderHasGarments.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => Order_1.Order, order => order.garments, { nullable: false, onDelete: "CASCADE" }),
    (0, typeorm_1.JoinColumn)({ name: "order_id" }),
    __metadata("design:type", Order_1.Order)
], OrderHasGarments.prototype, "order", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => Garment_1.Garment, garment => garment.orderHasGarments, { nullable: false, onDelete: "CASCADE" }),
    (0, typeorm_1.JoinColumn)({ name: "garment_id" }),
    __metadata("design:type", Garment_1.Garment)
], OrderHasGarments.prototype, "garment", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], OrderHasGarments.prototype, "quantity", void 0);
exports.OrderHasGarments = OrderHasGarments = __decorate([
    (0, typeorm_1.Entity)("order_has_garments")
], OrderHasGarments);
