"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const orderController_1 = __importDefault(require("../controllers/orderController"));
const ordersRoutes = (0, express_1.Router)();
ordersRoutes.get('/', orderController_1.default.getAll);
ordersRoutes.get('/:id', orderController_1.default.getById);
ordersRoutes.post('/', orderController_1.default.create);
ordersRoutes.patch('/:id', orderController_1.default.patch);
ordersRoutes.delete('/:id', orderController_1.default.delete);
exports.default = ordersRoutes;
