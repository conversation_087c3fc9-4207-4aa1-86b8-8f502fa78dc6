"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const customerController_1 = __importDefault(require("../controllers/customerController"));
const customersRoutes = (0, express_1.Router)();
customersRoutes.get('/', customerController_1.default.getAll);
customersRoutes.get('/:id', customerController_1.default.getById);
customersRoutes.post('/', customerController_1.default.create);
customersRoutes.patch('/:id', customerController_1.default.patch);
customersRoutes.delete('/:id', customerController_1.default.delete);
exports.default = customersRoutes;
