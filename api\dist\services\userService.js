"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
const bcrypt_1 = __importDefault(require("bcrypt"));
const userRepository_1 = require("../repositories/userRepository");
const personRepository_1 = require("../repositories/personRepository");
const staffRepository_1 = require("../repositories/staffRepository");
const User_1 = require("../entities/User");
const Staff_1 = require("../entities/Staff");
class UserService {
    static getAllUsers() {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const users = yield userRepository_1.userRepository.find({
                    where: { is_active: true },
                    relations: {
                        person: true,
                        staff: true
                    }
                });
                return users;
            }
            catch (error) {
                console.error(error);
                return undefined;
            }
        });
    }
    static getUserById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const user = yield userRepository_1.userRepository.findOne({
                    where: { id, is_active: true },
                    relations: {
                        person: true,
                        staff: true
                    }
                });
                return user;
            }
            catch (error) {
                console.error(error);
                return undefined;
            }
        });
    }
    static registerUser(userData) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a;
            try {
                const { comissionRate, person_id } = userData, newUserData = __rest(userData, ["comissionRate", "person_id"]);
                // Hash password
                const hashedPassword = yield UserService.hashPassword(newUserData.password);
                // Find person
                const person = yield personRepository_1.personRepository.findOneBy({ id: person_id });
                if (!person) {
                    throw new Error(`Person with id ${person_id} not found`);
                }
                // Create user
                const user = new User_1.User();
                user.username = newUserData.username;
                user.password = hashedPassword;
                user.userType = newUserData.userType;
                user.is_active = (_a = newUserData.is_active) !== null && _a !== void 0 ? _a : true;
                user.person = person;
                const savedUser = yield userRepository_1.userRepository.save(user);
                // If user is staff, create staff record
                if (newUserData.userType === 'staff') {
                    const staff = new Staff_1.Staff();
                    staff.user_id = savedUser.id;
                    staff.comissionRate = comissionRate || 0;
                    staff.user = savedUser;
                    yield staffRepository_1.staffRepository.save(staff);
                }
                return savedUser;
            }
            catch (error) {
                console.error(error);
                throw error;
            }
        });
    }
    static login(username, password) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const user = yield userRepository_1.userRepository.findOne({
                    where: { username, is_active: true },
                    relations: {
                        person: true,
                        staff: true
                    }
                });
                if (!user) {
                    return null;
                }
                const isPasswordValid = yield bcrypt_1.default.compare(password, user.password);
                if (!isPasswordValid) {
                    return null;
                }
                // Remove password from response
                const { password: _ } = user, userWithoutPassword = __rest(user, ["password"]);
                return userWithoutPassword;
            }
            catch (error) {
                console.error(error);
                throw error;
            }
        });
    }
    static checkUsername(username) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const user = yield userRepository_1.userRepository.findOne({
                    where: { username }
                });
                return !!user;
            }
            catch (error) {
                console.error(error);
                throw error;
            }
        });
    }
    static updateUser(id, updateData) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const user = yield userRepository_1.userRepository.findOne({
                    where: { id },
                    relations: {
                        person: true,
                        staff: true
                    }
                });
                if (!user) {
                    return null;
                }
                // Update user fields
                if (updateData.username !== undefined) {
                    user.username = updateData.username;
                }
                if (updateData.password !== undefined) {
                    user.password = yield UserService.hashPassword(updateData.password);
                }
                if (updateData.userType !== undefined) {
                    user.userType = updateData.userType;
                }
                if (updateData.is_active !== undefined) {
                    user.is_active = updateData.is_active;
                }
                // Update person if provided
                if (updateData.person) {
                    yield personRepository_1.personRepository.save(updateData.person);
                }
                // Update staff commission rate if provided and user is staff
                if (updateData.comissionRate !== undefined && user.userType === User_1.UserType.STAFF) {
                    if (user.staff) {
                        user.staff.comissionRate = updateData.comissionRate;
                        yield staffRepository_1.staffRepository.save(user.staff);
                    }
                    else {
                        // Create staff record if it doesn't exist
                        const staff = new Staff_1.Staff();
                        staff.user_id = user.id;
                        staff.comissionRate = updateData.comissionRate;
                        staff.user = user;
                        yield staffRepository_1.staffRepository.save(staff);
                    }
                }
                const updatedUser = yield userRepository_1.userRepository.save(user);
                return updatedUser;
            }
            catch (error) {
                console.error(error);
                throw error;
            }
        });
    }
    static deleteUser(id) {
        return __awaiter(this, void 0, void 0, function* () {
            try {
                const user = yield userRepository_1.userRepository.findOneBy({ id });
                if (!user) {
                    return false;
                }
                // Soft delete by setting is_active to false
                user.is_active = false;
                yield userRepository_1.userRepository.save(user);
                return true;
            }
            catch (error) {
                console.error(error);
                return false;
            }
        });
    }
    static hashPassword(password) {
        return __awaiter(this, void 0, void 0, function* () {
            const saltRounds = 10;
            return yield bcrypt_1.default.hash(password, saltRounds);
        });
    }
}
exports.UserService = UserService;
