import type { Request, Response } from "express";
import { PersonService } from "../services/personService";
import { ICreateCustomerDto, IPatchCustomerDto } from "../dto/CustomerDto";
import { ICreatePersonDto, IPatchPersonDto } from "../dto/PersonDto";
import { customerRepository } from "../repositories/customerRepository";
import { personRepository } from "../repositories/personRepository";

class CustomerController {
    public async getAll(req: Request, res: Response) {
        try {
            const allCustomers = await customerRepository.find({
                relations: {
                    person: true
                }
            });
            res.status(200).json(allCustomers);
        } catch (error) {
            console.error(error);
            res.status(500).json({ error: 'Internal Server Error.' });
        }
    }

    public async getById (req: Request, res: Response) {
        const { id } = req.params;
        const customerId = parseInt(id, 10);

        if (isNaN(customerId)) {
            return res.status(400).json({ message: 'ID do cliente inválido.' });
        }

        try {
            const customer = await customerRepository.findOne({
                where: { id: customerId },
                relations: {
                    person: true
                }
            });
            return customer ? res.status(200).json(customer) : res.status(404).json({ message: 'Cliente não encontrado.' });
        } catch (error) {
            console.error('Error fetching customer:', error);
            return res.status(500).json({ message: 'Error fetching customer' });
        }
    }

    public async create(req: Request, res: Response) {
        const { fullname, contact, cpf, cnpj, personType, address } = req.body;

        const personData: ICreatePersonDto = {
            fullname,
            contact,
            cpf,
            cnpj,
            personType
        };

        try {
            const newPerson = personRepository.create(personData);
            const savedPerson = await personRepository.save(newPerson);

            const customerData: ICreateCustomerDto = {
                person: savedPerson,
                address,
                is_active: true
            };

            const newCustomer = customerRepository.create(customerData);
            await customerRepository.save(newCustomer);

            res.status(201).json(newCustomer);
        } catch (error) {
            console.error(error);
            res.status(500).json({ error: 'Internal Server Error' });
        }
    }

    public async patch(req: Request, res: Response) {
        const { id } = req.params;
        const customerId = parseInt(id, 10);

        if (isNaN(customerId)) {
            return res.status(400).json({ message: 'ID de cliente inválido!' });
        }

        const { fullname, contact, cpf, cnpj, address } = req.body;

        const personUpdatedData: IPatchPersonDto = {
            ...(fullname && { fullname }),
            ...(contact && { contact }),
            ...(cpf !== undefined && { cpf }),
            ...(cnpj !== undefined && { cnpj })
        };

        const customerUpdatedData: IPatchCustomerDto = {
            ...(address && { address })
        };

        const wasPersonUpdated = Object.keys(personUpdatedData).length > 0;
        const wasCustomerUpdated = Object.keys(customerUpdatedData).length > 0;

        if (!wasPersonUpdated && !wasCustomerUpdated) {
            return res.status(400).json({ message: 'Nenhum campo para atualizar.' });
        }

        try {
            const customer = await customerRepository.findOne({
                where: { id: customerId },
                relations: {
                    person: true
                }
            });

            if (customer) {
                const personId = customer.person.id;

                if (wasPersonUpdated) {
                    await personRepository.update(personId, personUpdatedData);
                }

                if (wasCustomerUpdated) {
                    await customerRepository.update(customerId, customerUpdatedData);
                }

                return res.status(200).json({ message: 'Cliente atualizado com sucesso!' });
            } else {
                return res.status(404).json({ message: 'Cliente não encontrado!' });
            }
        } catch (error) {
            console.error('Erro ao atualizar cliente:', error);
            return res.status(500).json({ message: 'Erro ao atualizar cliente!' });
        }
    }

    public async delete(req: Request, res: Response) {
        const {id} = req.params;
        const customerId = parseInt(id, 10);

        if (isNaN(customerId)) {
            return res.status(400).json({ message: 'ID de cliente inválido.' });
        }

        try {
            const customer = await customerRepository.findOneBy({ id: customerId });

            if (!customer) {
                return res.status(404).json({ message: 'Registro de cliente não encontrado.' });
            }

            await customerRepository.update(customerId, { is_active: false });

            return res.status(200).json({ message: 'Cliente deletado com sucesso!' });
        } catch (error) {
            console.error('Erro ao deletar cliente:', error);
            return res.status(500).json({ message: 'Erro ao deletar cliente!' });
        }
    }
}

export default new CustomerController();
