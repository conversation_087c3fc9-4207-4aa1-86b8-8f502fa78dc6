import { Entity, PrimaryGeneratedColumn, Column, OneToMany } from "typeorm";
import { User } from "./User";
import { Customer } from "./Customer";

export enum PersonType {
    INDIVIDUAL = "individual",
    LEGAL = "legal"
}

@Entity("person")
export class Person {
    @PrimaryGeneratedColumn()
    id!: number;

    @Column({ length: 100 })
    fullname!: string;

    @Column({ length: 25, nullable: true })
    contact!: string;

    @Column({
        type: "enum",
        enum: PersonType,
        nullable: true
    })
    personType!: PersonType;

    @Column({ length: 20, nullable: true })
    cpf!: string;

    @Column({ length: 25, nullable: true })
    cnpj!: string;

    @OneToMany(() => User, user => user.person, { nullable: false })
    users!: User[];

    @OneToMany(() => Customer, customer => customer.person, { nullable: false })
    customers!: Customer[];
}