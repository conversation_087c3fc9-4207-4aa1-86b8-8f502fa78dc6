import { Footer } from "../../../components/Footer";
import { Header } from "../../../components/Header";

import { useState } from "react";
import { CadPedido } from "../../../components/CadPedido";
import { CadModelo } from ".././../../components/CadModelo/index";

export function NewOrder() {
	const [isHidden, setIsHidden] = useState(true);
	const [refreshData, setRefreshData] = useState(false);

	return (
		<>
			<div className="h-screen flex flex-col">
				<Header pagename="Novo Pedido" href={"/"} $logout={false} />

				<main className="flex-grow">
					<h1 className="text-center my-5 text-2xl font-bold">
						Cadastre um novo pedido:
					</h1>

					<div
						id="forms"
						className="flex flex-col items-center w-full gap-4 lg:justify-center mb-4"
					>
						<CadPedido
							$isntupdate={true}
							$isupdate={false}
							refreshTrigger={refreshData}
						/>

						<button
							type="button"
							className="bg-blue-300 border border-gray-300 rounded px-4 py-2"
							onClick={() => setIsHidden(!isHidden)}
						>
							{isHidden ? "Cadastrar Novo Modelo" : "Fechar Cadastro de Modelo"}
						</button>

						<CadModelo
							$btnactivated={false}
							$return={false}
							$hidden={isHidden}
							onSubmit={() => setRefreshData((prev) => !prev)}
						/>
					</div>
				</main>

				<Footer />
			</div>
		</>
	);
}
