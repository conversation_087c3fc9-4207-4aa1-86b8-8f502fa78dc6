"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Person = exports.PersonType = void 0;
const typeorm_1 = require("typeorm");
const User_1 = require("./User");
const Customer_1 = require("./Customer");
var PersonType;
(function (PersonType) {
    PersonType["INDIVIDUAL"] = "individual";
    PersonType["LEGAL"] = "legal";
})(PersonType || (exports.PersonType = PersonType = {}));
let Person = class Person {
};
exports.Person = Person;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)(),
    __metadata("design:type", Number)
], Person.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 100 }),
    __metadata("design:type", String)
], Person.prototype, "fullname", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 25, nullable: true }),
    __metadata("design:type", String)
], Person.prototype, "contact", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: "enum",
        enum: PersonType,
        nullable: true
    }),
    __metadata("design:type", String)
], Person.prototype, "personType", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 20, nullable: true }),
    __metadata("design:type", String)
], Person.prototype, "cpf", void 0);
__decorate([
    (0, typeorm_1.Column)({ length: 25, nullable: true }),
    __metadata("design:type", String)
], Person.prototype, "cnpj", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => User_1.User, user => user.person, { nullable: false }),
    __metadata("design:type", Array)
], Person.prototype, "users", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => Customer_1.Customer, customer => customer.person, { nullable: false }),
    __metadata("design:type", Array)
], Person.prototype, "customers", void 0);
exports.Person = Person = __decorate([
    (0, typeorm_1.Entity)("person")
], Person);
