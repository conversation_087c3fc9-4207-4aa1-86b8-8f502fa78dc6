import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, ManyToOne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany } from "typeorm";
import { Customer } from "./Customer";
import { User } from "./User";
import { OrderHasGarments } from "./OrderHasGarments";

@Entity("orders")
export class Order {
    @PrimaryGeneratedColumn()
    id!: number;

    @Column({ type: "date" })
    date!: Date;

    @Column({ type: "time" })
    time!: string;

    @Column({ length: 50 })
    deadline!: string;

    @Column({ length: 25 })
    status!: string;

    @ManyToOne(() => Customer, customer => customer.orders, { nullable: false })
    @JoinColumn({ name: "customer_id" })
    customer!: Customer;

    @ManyToOne(() => User, user => user.orders, { nullable: false })
    @JoinColumn({ name: "user_id" })
    user!: User;

    @OneToMany(() => OrderHasGarments, orderHasGarments => orderHasGarments.order, { nullable: false })
    garments!: OrderHasGarments[];
}