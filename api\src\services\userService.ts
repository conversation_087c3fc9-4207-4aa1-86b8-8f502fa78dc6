import bcrypt from 'bcrypt';
import { userRepository } from '../repositories/userRepository';
import { personRepository } from '../repositories/personRepository';
import { staffRepository } from '../repositories/staffRepository';
import { User, UserType } from '../entities/User';
import { Person, PersonType } from '../entities/Person';
import { Staff } from '../entities/Staff';
import { ICreateUserDto, IPatchUserDto } from '../dto/UserDto';

export class UserService {
    
    public static async getAllUsers() {
        try {
            const users = await userRepository.find({
                where: { is_active: true },
                relations: {
                    person: true,
                    staff: true
                }
            });
            return users;
        } catch (error) {
            console.error(error);
            return undefined;
        }
    }

    public static async getUserById(id: number) {
        try {
            const user = await userRepository.findOne({
                where: { id, is_active: true },
                relations: {
                    person: true,
                    staff: true
                }
            });
            return user;
        } catch (error) {
            console.error(error);
            return undefined;
        }
    }

    public static async registerUser(userData: {
        username: string;
        password: string;
        userType: 'staff' | 'admin';
        is_active?: boolean;
        person_id: number;
        comissionRate?: number;
    }) {
        try {
            const { comissionRate, person_id, ...newUserData } = userData;
            
            // Hash password
            const hashedPassword = await UserService.hashPassword(newUserData.password);
            
            // Find person
            const person = await personRepository.findOneBy({ id: person_id });
            if (!person) {
                throw new Error(`Person with id ${person_id} not found`);
            }

            // Create user
            const user = new User();
            user.username = newUserData.username;
            user.password = hashedPassword;
            user.userType = newUserData.userType as UserType;
            user.is_active = newUserData.is_active ?? true;
            user.person = person;

            const savedUser = await userRepository.save(user);

            // If user is staff, create staff record
            if (newUserData.userType === 'staff') {
                const staff = new Staff();
                staff.user_id = savedUser.id;
                staff.comissionRate = comissionRate || 0;
                staff.user = savedUser;
                
                await staffRepository.save(staff);
            }

            return savedUser;
        } catch (error) {
            console.error(error);
            throw error;
        }
    }

    public static async login(username: string, password: string) {
        try {
            const user = await userRepository.findOne({
                where: { username, is_active: true },
                relations: {
                    person: true,
                    staff: true
                }
            });

            if (!user) {
                return null;
            }

            const isPasswordValid = await bcrypt.compare(password, user.password);
            if (!isPasswordValid) {
                return null;
            }

            // Remove password from response
            const { password: _, ...userWithoutPassword } = user;
            return userWithoutPassword;
        } catch (error) {
            console.error(error);
            throw error;
        }
    }

    public static async checkUsername(username: string): Promise<boolean> {
        try {
            const user = await userRepository.findOne({
                where: { username }
            });
            return !!user;
        } catch (error) {
            console.error(error);
            throw error;
        }
    }

    public static async updateUser(id: number, updateData: IPatchUserDto) {
        try {
            const user = await userRepository.findOne({
                where: { id },
                relations: {
                    person: true,
                    staff: true
                }
            });

            if (!user) {
                return null;
            }

            // Update user fields
            if (updateData.username !== undefined) {
                user.username = updateData.username;
            }
            if (updateData.password !== undefined) {
                user.password = await UserService.hashPassword(updateData.password);
            }
            if (updateData.userType !== undefined) {
                user.userType = updateData.userType;
            }
            if (updateData.is_active !== undefined) {
                user.is_active = updateData.is_active;
            }

            // Update person if provided
            if (updateData.person) {
                await personRepository.save(updateData.person);
            }

            // Update staff commission rate if provided and user is staff
            if (updateData.comissionRate !== undefined && user.userType === UserType.STAFF) {
                if (user.staff) {
                    user.staff.comissionRate = updateData.comissionRate;
                    await staffRepository.save(user.staff);
                } else {
                    // Create staff record if it doesn't exist
                    const staff = new Staff();
                    staff.user_id = user.id;
                    staff.comissionRate = updateData.comissionRate;
                    staff.user = user;
                    await staffRepository.save(staff);
                }
            }

            const updatedUser = await userRepository.save(user);
            return updatedUser;
        } catch (error) {
            console.error(error);
            throw error;
        }
    }

    public static async deleteUser(id: number) {
        try {
            const user = await userRepository.findOneBy({ id });
            if (!user) {
                return false;
            }

            // Soft delete by setting is_active to false
            user.is_active = false;
            await userRepository.save(user);
            return true;
        } catch (error) {
            console.error(error);
            return false;
        }
    }

    private static async hashPassword(password: string): Promise<string> {
        const saltRounds = 10;
        return await bcrypt.hash(password, saltRounds);
    }
}
