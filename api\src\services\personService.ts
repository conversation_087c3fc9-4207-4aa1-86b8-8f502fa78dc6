import { ICreatePersonDto, IPatchPersonDto } from '../dto/PersonDto';
import { Person, PersonType } from '../entities/Person';
import { personRepository } from '../repositories/personRepository';

export class PersonService {
    
    public static async registerPerson(personData: ICreatePersonDto): Promise<number> {
        try {
            const person = new Person();
            person.fullname = personData.fullname;
            person.contact = personData.contact || '';
            person.personType = personData.personType || PersonType.INDIVIDUAL;
            person.cpf = personData.cpf || '';
            person.cnpj = personData.cnpj || '';

            const savedPerson = await personRepository.save(person);
            return savedPerson.id;
        } catch (error) {
            console.error(error);
            throw error;
        }
    }

    public static async findPersonById(personId: number): Promise<Person | null> {
        try {
            const person = await personRepository.findOneBy({ id: personId });
            return person || null;
        } catch (error) {
            console.error(error);
            throw error;
        }
    }

    public static async getAllPersons(): Promise<Person[]> {
        try {
            const persons = await personRepository.find();
            return persons;
        } catch (error) {
            console.error(error);
            throw error;
        }
    }

    public static async updatePerson(id: number, updateData: IPatchPersonDto): Promise<Person | null> {
        try {
            const person = await personRepository.findOneBy({ id });
            if (!person) {
                return null;
            }

            if (updateData.fullname !== undefined) {
                person.fullname = updateData.fullname;
            }
            if (updateData.contact !== undefined) {
                person.contact = updateData.contact;
            }
            if (updateData.personType !== undefined) {
                person.personType = updateData.personType;
            }
            if (updateData.cpf !== undefined) {
                person.cpf = updateData.cpf;
            }
            if (updateData.cnpj !== undefined) {
                person.cnpj = updateData.cnpj;
            }

            const updatedPerson = await personRepository.save(person);
            return updatedPerson;
        } catch (error) {
            console.error(error);
            throw error;
        }
    }

    public static async deletePerson(id: number): Promise<boolean> {
        try {
            const result = await personRepository.delete(id);
            return result.affected ? result.affected > 0 : false;
        } catch (error) {
            console.error(error);
            return false;
        }
    }

    public static async checkCPForCNPJ(dataType: 'cpf' | 'cnpj', data: string): Promise<boolean> {
        try {
            const whereCondition = dataType === 'cpf' ? { cpf: data } : { cnpj: data };
            const person = await personRepository.findOne({
                where: whereCondition
            });
            return !!person;
        } catch (error) {
            console.error(`Error getting CPF or CNPJ: ${error}`);
            throw new Error("Error getting CPF or CNPJ!");
        }
    }
}
