import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, ManyToMany } from "typeorm";
import { Garment } from "./Garment";

@Entity("raw_material")
export class RawMaterial {
    @PrimaryGeneratedColumn()
    id!: number;

    @Column({ length: 50 })
    name!: string;

    @Column({ length: 50 })
    manufacturer!: string;

    @Column({ type: "float" })
    price_per_weight!: number;

    @Column({ length: 50 })
    color!: string;

    @Column({ type: "float" })
    weight!: number;

    @Column()
    is_active!: boolean;

    @ManyToMany(() => Garment, garment => garment.rawMaterials, { nullable: false })
    garments!: Garment[];
}